"use client";

import { useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { FullPageLoadingSpinner } from "@/components/ui/LoadingSpinner";
import { useAuth } from "@/hooks/use-auth";
import { useSocket } from "@/hooks/use-socket";
import { trpc } from "@/lib/trpc/client";
import { BellIcon } from "@heroicons/react/24/solid";
import { format } from "date-fns";
import { MessageType } from "@repo/server/src/types/chat";

export function ChatList() {
  const router = useRouter();
  const { user } = useAuth();
  const { socket, connectionState, markAsRead } = useSocket();
  const { data: chats, isLoading } = trpc.chat.getChats.useQuery();
  const utils = trpc.useUtils();

  useEffect(() => {
    if (!socket) {
      return;
    }

    const handleNewMessage = () => {
      // Simply invalidate the query to refetch the latest data
      utils.chat.getChats.invalidate();
    };

    const handleMessagesRead = () => {
      // Simply invalidate the query to refetch the latest data
      utils.chat.getChats.invalidate();
    };
    socket.on("newMessage", handleNewMessage);
    socket.on("messagesRead", handleMessagesRead);

    return () => {
      socket.off("newMessage", handleNewMessage);
      socket.off("messagesRead", handleMessagesRead);
    };
  }, [socket, connectionState.isConnected, user?.id, utils, markAsRead]);

  if (isLoading) return <FullPageLoadingSpinner />;
  if (!chats?.length) {
    return (
      <div className="tw-flex tw-flex-col tw-items-center tw-justify-center tw-h-full tw-text-aims-text-secondary">
        <p className="tw-text-xl">No chats to display</p>
      </div>
    );
  }

  console.log(chats)

  return (
    <div className="tw-flex tw-flex-col tw-gap-2">
      {chats.map((chat) => {
        // Get the other participant (not the current user)
        const otherParticipant = chat.participants.find(
          (p) => p.id !== user?.id,
        );

        // Check if there are unread messages
        const hasUnreadMessages =
          chat.lastMessage &&
          chat.lastMessage.sender &&
          chat.lastMessage.sender.id !== user?.id &&
          !chat.lastMessage.readBy.includes(user?.id || "");

        return (
          <button
            key={chat.id}
            onClick={() => {
              // Mark messages as read when clicking into chat
              if (hasUnreadMessages) {
                markAsRead(chat.id);
              }
              router.push(`/app/chat/${chat.id}`);
            }}
            className={`tw-flex tw-items-center tw-gap-3 tw-p-4 tw-rounded-lg tw-transition-colors ${
              hasUnreadMessages
                ? "tw-bg-aims-dark-3 hover:tw-bg-aims-dark-4"
                : "tw-bg-aims-dark-2 hover:tw-bg-aims-dark-3"
            }`}
          >
            <div className="tw-relative">
              <Image
                src={
                  otherParticipant?.profilePicture?.url || "/no-profile-pic.jpg"
                }
                alt={otherParticipant?.name || "User"}
                className="tw-w-12 tw-h-12 tw-rounded-full tw-object-cover"
                width={48}
                height={48}
              />
              {hasUnreadMessages && (
                <div className="tw-absolute -tw-top-1 -tw-right-1 tw-w-5 tw-h-5 tw-bg-blue-500 tw-rounded-full tw-flex tw-items-center tw-justify-center">
                  <BellIcon className="tw-w-3 tw-h-3 tw-text-white" />
                </div>
              )}
            </div>
            <div className="tw-flex-1 tw-min-w-0">
              <div className="tw-flex tw-justify-between tw-items-start">
                <h3 className="tw-text-3xl tw-font-medium tw-text-aims-text-primary tw-truncate">
                  {otherParticipant?.name}
                  {otherParticipant?.brand?.name && (
                    <span className="tw-text-aims-text-secondary tw-ml-1">
                      - {otherParticipant?.brand?.name}
                    </span>
                  )}
                </h3>
                {chat.lastMessage && (
                  <span className="tw-text-xs tw-text-aims-text-secondary">
                    {format(new Date(chat.lastMessage.createdAt), "MMM d, yyyy 'at' h:mm a")}
                  </span>
                )}
              </div>
              {chat.lastMessage && (
                <p className="tw-text-sm tw-text-aims-dark-6 tw-truncate tw-text-left">
                  {chat.lastMessage.type === MessageType.CAMPAIGN_INVITE ? "Campaign Invitation" : chat.lastMessage.type === MessageType.CAMPAIGN_APPLICATION ? "Campaign Application" : chat.lastMessage.type === MessageType.CAMPAIGN_ACCEPT ? "Campaign Accepted" : chat.lastMessage.type === MessageType.CAMPAIGN_REJECT ? "Campaign Rejected" : chat.lastMessage.type === MessageType.APPLICATION_ACCEPTED ? "Application Accepted" : chat.lastMessage.type === MessageType.APPLICATION_REJECTED ? "Application Rejected" : chat.lastMessage.type === MessageType.DELIVERABLE_SUBMISSION ? "Deliverable Submitted" : chat.lastMessage.content}
                </p>
              )}
            </div>
          </button>
        );
      })}
    </div>
  );
}
