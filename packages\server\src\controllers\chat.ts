import { TRPCError } from "@trpc/server";
import { Schema, Types } from "mongoose";

import { getIO } from "../lib/socket";
import AthleteModel from "../models/athlete";
import BrandModel from "../models/brand";
import ChatModel, { ChatDocument } from "../models/chat";
import MessageModel from "../models/message";

interface PopulatedParticipant {
  _id: Types.ObjectId;
  name: string;
  userType: string;
  athlete?: {
    profilePicture?: {
      url: string;
      key: string;
      uploadedAt: Date;
    };
  };
  brand?: {
    logo?: {
      url: string;
      key: string;
      uploadedAt: Date;
    };
  };
}

interface PopulatedMessage {
  _id: Types.ObjectId;
  content: string;
  type: string;
  senderId: PopulatedParticipant;
  createdAt: Date;
  readBy: Types.ObjectId[];
}

// Helper function to convert document to client format
const toClient = (doc: any) => ({
  id: doc._id.toString(),
  ...doc.toObject(),
  _id: undefined,
  __v: undefined,
});

export const getChatById = async (userId: string, chatId: string) => {
  try {
    const chat = await ChatModel.findOne({
      _id: new Types.ObjectId(chatId),
      participants: new Types.ObjectId(userId),
    })
      .populate<{
        participants: PopulatedParticipant[];
      }>({
        path: "participants",
        select: "name userType",
        model: "Users",
      })
      .populate<{
        lastMessage: PopulatedMessage;
      }>({
        path: "lastMessage",
        populate: {
          path: "senderId",
          select: "name userType",
          model: "Users",
        },
        select: "content type senderId createdAt readBy",
      })
      .lean();

    if (!chat) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Chat not found",
      });
    }

    // Get all unique participant IDs
    const participantIds = new Set<string>();
    chat.participants.forEach((p) => {
      participantIds.add(p._id.toString());
    });

    // Fetch athlete profiles
    const athleteProfiles = await AthleteModel.find({
      userId: {
        $in: Array.from(participantIds).map((id) => new Types.ObjectId(id)),
      },
    }).lean();

    // Fetch brand profiles
    const brandProfiles = await BrandModel.find({
      userId: {
        $in: Array.from(participantIds).map((id) => new Types.ObjectId(id)),
      },
    }).lean();

    // Create lookup maps
    const athleteMap = new Map(
      athleteProfiles.map((p) => [p.userId.toString(), p]),
    );
    const brandMap = new Map(
      brandProfiles.map((p) => [p.userId.toString(), p]),
    );

    const participants = chat.participants.map((p) => {
      const userId = p._id.toString();
      const athleteProfile = athleteMap.get(userId);
      const brandProfile = brandMap.get(userId);

      // Use athlete's profile name if available, otherwise fall back to user name
      const displayName =
        p.userType === "athlete" && athleteProfile?.name
          ? athleteProfile.name
          : p.name;

      const profilePicture =
        p.userType === "athlete"
          ? athleteProfile?.profilePicture
          : brandProfile?.logo;

      return {
        id: userId,
        name: displayName,
        userType: p.userType,
        profilePicture: profilePicture || null,
        brand: brandProfile
          ? {
              id: brandProfile._id.toString(),
              name: brandProfile.companyName,
            }
          : null,
      };
    });

    let lastMessageSender = null;
    if (chat.lastMessage) {
      const senderId = chat.lastMessage.senderId._id.toString();
      const athleteProfile = athleteMap.get(senderId);
      const brandProfile = brandMap.get(senderId);

      const displayName =
        chat.lastMessage.senderId.userType === "athlete" && athleteProfile?.name
          ? athleteProfile.name
          : chat.lastMessage.senderId.name;

      const profilePicture =
        chat.lastMessage.senderId.userType === "athlete"
          ? athleteProfile?.profilePicture
          : brandProfile?.logo;

      lastMessageSender = {
        id: senderId,
        name: displayName,
        userType: chat.lastMessage.senderId.userType,
        profilePicture: profilePicture || null,
        brand: brandProfile
          ? {
              id: brandProfile._id.toString(),
              name: brandProfile.companyName,
            }
          : null,
      };
    }

    return {
      id: chat._id.toString(),
      participants,
      type: chat.type,
      campaignId: chat.campaignId?.toString(),
      lastMessage: chat.lastMessage
        ? {
            id: chat.lastMessage._id.toString(),
            content: chat.lastMessage.content,
            type: chat.lastMessage.type,
            sender: lastMessageSender,
            createdAt: chat.lastMessage.createdAt.toISOString(),
            readBy:
              chat.lastMessage.readBy?.map((id: Types.ObjectId) =>
                id.toString(),
              ) || [],
          }
        : null,
      createdAt: chat.createdAt.toISOString(),
      updatedAt: chat.updatedAt.toISOString(),
    };
  } catch (error) {
    console.error("Error fetching chat:", error);
    throw new TRPCError({
      code: "INTERNAL_SERVER_ERROR",
      message: "Failed to fetch chat",
    });
  }
};

export const getChats = async (userId: string) => {
  try {
    const chats = await ChatModel.find({
      participants: new Types.ObjectId(userId),
    })
      .populate<{
        participants: PopulatedParticipant[];
      }>({
        path: "participants",
        select: "name userType",
        model: "Users",
      })
      .populate<{
        lastMessage: PopulatedMessage;
      }>({
        path: "lastMessage",
        populate: {
          path: "senderId",
          select: "name userType",
          model: "Users",
        },
        select: "content type senderId createdAt readBy",
      })
      .sort({ updatedAt: -1 })
      .lean();

    // Get all unique participant IDs
    const participantIds = new Set<string>();
    chats.forEach((chat) => {
      chat.participants.forEach((p) => {
        participantIds.add(p._id.toString());
      });
    });

    // Fetch athlete profiles
    const athleteProfiles = await AthleteModel.find({
      userId: {
        $in: Array.from(participantIds).map((id) => new Types.ObjectId(id)),
      },
    }).lean();

    // Fetch brand profiles
    const brandProfiles = await BrandModel.find({
      userId: {
        $in: Array.from(participantIds).map((id) => new Types.ObjectId(id)),
      },
    }).lean();

    // Create lookup maps
    const athleteMap = new Map(
      athleteProfiles.map((p) => [p.userId.toString(), p]),
    );
    const brandMap = new Map(
      brandProfiles.map((p) => [p.userId.toString(), p]),
    );

    // Get profile information for lastMessage senders
    const lastMessageSenderIds = new Set<string>();
    chats.forEach((chat) => {
      if (chat.lastMessage?.senderId) {
        lastMessageSenderIds.add(chat.lastMessage.senderId._id.toString());
      }
    });

    // Fetch athlete profiles for lastMessage senders
    const lastMessageAthleteProfiles = await AthleteModel.find({
      userId: {
        $in: Array.from(lastMessageSenderIds).map(
          (id) => new Types.ObjectId(id),
        ),
      },
    }).lean();

    // Fetch brand profiles for lastMessage senders
    const lastMessageBrandProfiles = await BrandModel.find({
      userId: {
        $in: Array.from(lastMessageSenderIds).map(
          (id) => new Types.ObjectId(id),
        ),
      },
    }).lean();

    // Create lookup maps for lastMessage senders
    const lastMessageAthleteMap = new Map(
      lastMessageAthleteProfiles.map((p) => [p.userId.toString(), p]),
    );
    const lastMessageBrandMap = new Map(
      lastMessageBrandProfiles.map((p) => [p.userId.toString(), p]),
    );

    const transformedChats = chats.map((chat) => {
      const participants = chat.participants.map((p) => {
        const userId = p._id.toString();
        const athleteProfile = athleteMap.get(userId);
        const brandProfile = brandMap.get(userId);

        // Use athlete's profile name if available, otherwise fall back to user name
        const displayName =
          p.userType === "athlete" && athleteProfile?.name
            ? athleteProfile.name
            : p.name;

        const profilePicture =
          p.userType === "athlete"
            ? athleteProfile?.profilePicture
            : brandProfile?.logo;

        return {
          id: userId,
          name: displayName,
          userType: p.userType,
          profilePicture: profilePicture || null,
          brand: brandProfile
            ? {
                id: brandProfile._id.toString(),
                name: brandProfile.companyName,
              }
            : null,
        };
      });

      // Get lastMessage sender profile information
      let lastMessageSender = null;
      if (chat.lastMessage) {
        const senderId = chat.lastMessage.senderId._id.toString();
        const athleteProfile = lastMessageAthleteMap.get(senderId);
        const brandProfile = lastMessageBrandMap.get(senderId);

        // Use athlete's profile name if available, otherwise fall back to user name
        const displayName =
          chat.lastMessage.senderId.userType === "athlete" &&
          athleteProfile?.name
            ? athleteProfile.name
            : chat.lastMessage.senderId.name;

        const profilePicture =
          chat.lastMessage.senderId.userType === "athlete"
            ? athleteProfile?.profilePicture
            : brandProfile?.logo;

        lastMessageSender = {
          id: senderId,
          name: displayName,
          userType: chat.lastMessage.senderId.userType,
          profilePicture: profilePicture || null,
          brand: brandProfile
            ? {
                id: brandProfile._id.toString(),
                name: brandProfile.companyName,
              }
            : null,
        };
      }

      return {
        id: chat._id.toString(),
        participants,
        type: chat.type,
        campaignId: chat.campaignId?.toString(),
        lastMessage: chat.lastMessage
          ? {
              id: chat.lastMessage._id.toString(),
              content: chat.lastMessage.content,
              type: chat.lastMessage.type,
              sender: lastMessageSender,
              createdAt: chat.lastMessage.createdAt.toISOString(),
              readBy:
                chat.lastMessage.readBy?.map((id: Types.ObjectId) =>
                  id.toString(),
                ) || [],
            }
          : null,
        createdAt: chat.createdAt.toISOString(),
        updatedAt: chat.updatedAt.toISOString(),
      };
    });

    return transformedChats;
  } catch (error) {
    console.error("Error in getChats:", error);
    throw error;
  }
};

export const getMessages = async (
  chatId: string,
  userId: string,
  before?: string,
  limit: number = 50,
) => {
  try {
    const chat = await ChatModel.findById(new Types.ObjectId(chatId));
    if (!chat) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Chat not found",
      });
    }

    // Check if user is a participant
    if (!chat.participants.some((p) => p.toString() === userId)) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "You are not a participant in this chat",
      });
    }

    const query: any = { chatId: chat._id };
    if (before) {
      query.createdAt = { $lt: new Date(before) };
    }

    const messages = await MessageModel.find(query)
      .sort({ createdAt: -1 })
      .limit(limit)
      .populate<{
        senderId: PopulatedParticipant;
      }>("senderId", "name userType")
      .lean();

    // Reverse the messages to get chronological order (oldest to newest)
    messages.reverse();

    // Get all unique sender IDs
    const senderIds = new Set<string>();
    messages.forEach((msg) => {
      senderIds.add(msg.senderId._id.toString());
    });

    // Fetch athlete profiles
    const athleteProfiles = await AthleteModel.find({
      userId: {
        $in: Array.from(senderIds).map((id) => new Types.ObjectId(id)),
      },
    }).lean();

    // Fetch brand profiles
    const brandProfiles = await BrandModel.find({
      userId: {
        $in: Array.from(senderIds).map((id) => new Types.ObjectId(id)),
      },
    }).lean();

    // Create lookup maps
    const athleteMap = new Map(
      athleteProfiles.map((p) => [p.userId.toString(), p]),
    );
    const brandMap = new Map(
      brandProfiles.map((p) => [p.userId.toString(), p]),
    );

    return messages.map((msg) => {
      const senderId = msg.senderId._id.toString();
      const athleteProfile = athleteMap.get(senderId);
      const brandProfile = brandMap.get(senderId);

      // Use athlete's profile name if available, otherwise fall back to user name
      const displayName =
        msg.senderId.userType === "athlete" && athleteProfile?.name
          ? athleteProfile.name
          : msg.senderId.name;

      const profilePicture =
        msg.senderId.userType === "athlete"
          ? athleteProfile?.profilePicture
          : brandProfile?.logo;

      return {
        id: msg._id.toString(),
        chatId: msg.chatId.toString(),
        content: msg.content,
        type: msg.type,
        campaignId: msg.campaignId?.toString(),
        contractId: msg.contractId?.toString(),
        sender: {
          id: senderId,
          name: displayName,
          userType: msg.senderId.userType,
          profilePicture: profilePicture || null,
          brand: brandProfile
            ? {
                id: brandProfile._id.toString(),
                name: brandProfile.companyName,
              }
            : null,
        },
        readBy: msg.readBy.map((id) => id.toString()),
        createdAt: msg.createdAt.toISOString(),
        updatedAt: msg.updatedAt.toISOString(),
      };
    });
  } catch (error) {
    console.error("Error in getMessages:", error);
    throw error;
  }
};

export const createChat = async (
  userId: string,
  participantIds: string[],
  type: string,
  campaignId?: string,
) => {
  // For direct chats, ensure exactly 2 participants
  if (type === "DIRECT" && participantIds.length !== 1) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Direct chats must have exactly 2 participants",
    });
  }

  // Check if direct chat already exists
  if (type === "DIRECT") {
    const existingChat = await ChatModel.findOne({
      type: "DIRECT",
      participants: {
        $all: [
          new Types.ObjectId(userId),
          new Types.ObjectId(participantIds[0]),
        ],
      },
    });

    if (existingChat) {
      return toClient(existingChat);
    }
  }

  const chat = await ChatModel.create({
    participants: [
      new Types.ObjectId(userId),
      ...participantIds.map((id) => new Types.ObjectId(id)),
    ],
    type,
    campaignId: campaignId ? new Types.ObjectId(campaignId) : undefined,
  });

  return toClient(chat);
};

export const sendMessage = async (
  chatId: string,
  userId: string,
  content: string,
  type: string,
  campaignId?: string,
  contractId?: string,
) => {
  const chat = await ChatModel.findById(new Types.ObjectId(chatId));
  if (!chat) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Chat not found",
    });
  }

  // Check if user is a participant
  if (!chat.participants.some((p) => p.toString() === userId)) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "You are not a participant in this chat",
    });
  }

  const message = await MessageModel.create({
    chatId: chat._id,
    senderId: new Types.ObjectId(userId),
    content,
    type,
    campaignId: campaignId ? new Types.ObjectId(campaignId) : undefined,
    contractId: contractId ? new Types.ObjectId(contractId) : undefined,
    readBy: [new Types.ObjectId(userId)],
  });

  // Update chat's last message
  chat.lastMessage = message._id as unknown as Schema.Types.ObjectId;
  await chat.save();

  // Populate the message with sender information (similar to socket handler)
  const populatedMessage = await MessageModel.findById(message._id)
    .populate<{
      senderId: any;
    }>("senderId", "name userType")
    .lean();

  if (populatedMessage) {
    const senderId = populatedMessage.senderId._id.toString();
    const [athleteProfile, brandProfile] = await Promise.all([
      require("../models/athlete")
        .default.findOne({
          userId: new Types.ObjectId(senderId),
        })
        .lean(),
      require("../models/brand")
        .default.findOne({ userId: new Types.ObjectId(senderId) })
        .lean(),
    ]);

    const displayName =
      populatedMessage.senderId.userType === "athlete" && athleteProfile?.name
        ? athleteProfile.name
        : populatedMessage.senderId.name;

    const profilePicture =
      populatedMessage.senderId.userType === "athlete"
        ? athleteProfile?.profilePicture
        : brandProfile?.logo;

    // Emit to all participants in the chat with properly formatted message
    const io = getIO();
    console.log("Socket.io instance available:", !!io);

    if (io) {
      const messageData = {
        id: message._id.toString(),
        chatId: chatId,
        content: content,
        type: type,
        campaignId: message.campaignId?.toString(),
        contractId: message.contractId?.toString(),
        sender: {
          id: senderId,
          name: displayName,
          userType: populatedMessage.senderId.userType,
          profilePicture: profilePicture || null,
          brand: brandProfile
            ? {
                id: brandProfile._id.toString(),
                name: brandProfile.companyName,
              }
            : null,
        },
        readBy: message.readBy.map((id: any) => id.toString()),
        createdAt: message.createdAt.toISOString(),
        updatedAt: message.updatedAt.toISOString(),
      };

      console.log("Emitting newMessage to room:", chatId);
      console.log("Message data:", messageData);

      io.to(chatId).emit("newMessage", messageData);

      console.log("Message emitted successfully");
    } else {
      console.log("Socket.io instance not available - message not emitted");
    }
  }

  return toClient(message);
};

export const markAsRead = async (chatId: string, userId: string) => {
  const chat = await ChatModel.findById(new Types.ObjectId(chatId));
  if (!chat) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Chat not found",
    });
  }

  // Check if user is a participant
  if (!chat.participants.some((p) => p.toString() === userId)) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: "You are not a participant in this chat",
    });
  }

  await MessageModel.updateMany(
    {
      chatId: chat._id,
      readBy: { $ne: new Types.ObjectId(userId) },
    },
    {
      $addToSet: { readBy: new Types.ObjectId(userId) },
    },
  );

  return { success: true };
};
